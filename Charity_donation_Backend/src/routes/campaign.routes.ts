import express from "express";
import {
	createCampaign,
	getCampaignById,
	updateCampaign,
	deleteCampaign,
	addCauseToCampaign,
} from "../controllers/campaign.controller";
import { authenticate } from "../middleware/auth.middleware";
import { authorize } from "../middleware/role.middleware";

const router = express.Router();

// Public routes
router.get("/:campaignId", getCampaignById);

// Protected routes (require authentication)
router.use(authenticate);

// Organization-only routes
router.post("/", authorize(["organization"]), createCampaign);
router.patch("/:campaignId", authorize(["organization"]), updateCampaign);
router.delete("/:campaignId", authorize(["organization"]), deleteCampaign);
router.post(
	"/:campaignId/causes",
	authorize(["organization"]),
	addCauseToCampaign
);

export default router;
